{"version": 3, "sources": ["../../@mui/material/esm/Typography/Typography.js", "../../@mui/material/esm/Typography/typographyClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled, internal_createExtendSxProp } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport { getTypographyUtilityClass } from \"./typographyClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst v6Colors = {\n  primary: true,\n  secondary: true,\n  error: true,\n  info: true,\n  success: true,\n  warning: true,\n  textPrimary: true,\n  textSecondary: true,\n  textDisabled: true\n};\nconst extendSxProp = internal_createExtendSxProp();\nconst useUtilityClasses = ownerState => {\n  const {\n    align,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, ownerState.align !== 'inherit' && `align${capitalize(align)}`, gutterBottom && 'gutterBottom', noWrap && 'noWrap', paragraph && 'paragraph']\n  };\n  return composeClasses(slots, getTypographyUtilityClass, classes);\n};\nexport const TypographyRoot = styled('span', {\n  name: 'MuiTypography',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.variant && styles[ownerState.variant], ownerState.align !== 'inherit' && styles[`align${capitalize(ownerState.align)}`], ownerState.noWrap && styles.noWrap, ownerState.gutterBottom && styles.gutterBottom, ownerState.paragraph && styles.paragraph];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  variants: [{\n    props: {\n      variant: 'inherit'\n    },\n    style: {\n      // Some elements, like <button> on Chrome have default font that doesn't inherit, reset this.\n      font: 'inherit',\n      lineHeight: 'inherit',\n      letterSpacing: 'inherit'\n    }\n  }, ...Object.entries(theme.typography).filter(([variant, value]) => variant !== 'inherit' && value && typeof value === 'object').map(([variant, value]) => ({\n    props: {\n      variant\n    },\n    style: value\n  })), ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter()).map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      color: (theme.vars || theme).palette[color].main\n    }\n  })), ...Object.entries(theme.palette?.text || {}).filter(([, value]) => typeof value === 'string').map(([color]) => ({\n    props: {\n      color: `text${capitalize(color)}`\n    },\n    style: {\n      color: (theme.vars || theme).palette.text[color]\n    }\n  })), {\n    props: ({\n      ownerState\n    }) => ownerState.align !== 'inherit',\n    style: {\n      textAlign: 'var(--Typography-textAlign)'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.noWrap,\n    style: {\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n      whiteSpace: 'nowrap'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.gutterBottom,\n    style: {\n      marginBottom: '0.35em'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.paragraph,\n    style: {\n      marginBottom: 16\n    }\n  }]\n})));\nconst defaultVariantMapping = {\n  h1: 'h1',\n  h2: 'h2',\n  h3: 'h3',\n  h4: 'h4',\n  h5: 'h5',\n  h6: 'h6',\n  subtitle1: 'h6',\n  subtitle2: 'h6',\n  body1: 'p',\n  body2: 'p',\n  inherit: 'p'\n};\nconst Typography = /*#__PURE__*/React.forwardRef(function Typography(inProps, ref) {\n  const {\n    color,\n    ...themeProps\n  } = useDefaultProps({\n    props: inProps,\n    name: 'MuiTypography'\n  });\n  const isSxColor = !v6Colors[color];\n  // TODO: Remove `extendSxProp` in v7\n  const props = extendSxProp({\n    ...themeProps,\n    ...(isSxColor && {\n      color\n    })\n  });\n  const {\n    align = 'inherit',\n    className,\n    component,\n    gutterBottom = false,\n    noWrap = false,\n    paragraph = false,\n    variant = 'body1',\n    variantMapping = defaultVariantMapping,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    align,\n    color,\n    className,\n    component,\n    gutterBottom,\n    noWrap,\n    paragraph,\n    variant,\n    variantMapping\n  };\n  const Component = component || (paragraph ? 'p' : variantMapping[variant] || defaultVariantMapping[variant]) || 'span';\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TypographyRoot, {\n    as: Component,\n    ref: ref,\n    className: clsx(classes.root, className),\n    ...other,\n    ownerState: ownerState,\n    style: {\n      ...(align !== 'inherit' && {\n        '--Typography-textAlign': align\n      }),\n      ...other.style\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Typography.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Set the text-align on the component.\n   * @default 'inherit'\n   */\n  align: PropTypes.oneOf(['center', 'inherit', 'justify', 'left', 'right']),\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary', 'success', 'error', 'info', 'warning', 'textPrimary', 'textSecondary', 'textDisabled']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the text will have a bottom margin.\n   * @default false\n   */\n  gutterBottom: PropTypes.bool,\n  /**\n   * If `true`, the text will not wrap, but instead will truncate with a text overflow ellipsis.\n   *\n   * Note that text overflow can only happen with block or inline-block level elements\n   * (the element needs to have a width in order to overflow).\n   * @default false\n   */\n  noWrap: PropTypes.bool,\n  /**\n   * If `true`, the element will be a paragraph element.\n   * @default false\n   * @deprecated Use the `component` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  paragraph: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Applies the theme typography styles.\n   * @default 'body1'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['body1', 'body2', 'button', 'caption', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'inherit', 'overline', 'subtitle1', 'subtitle2']), PropTypes.string]),\n  /**\n   * The component maps the variant prop to a range of different HTML element types.\n   * For instance, subtitle1 to `<h6>`.\n   * If you wish to change that mapping, you can provide your own.\n   * Alternatively, you can use the `component` prop.\n   * @default {\n   *   h1: 'h1',\n   *   h2: 'h2',\n   *   h3: 'h3',\n   *   h4: 'h4',\n   *   h5: 'h5',\n   *   h6: 'h6',\n   *   subtitle1: 'h6',\n   *   subtitle2: 'h6',\n   *   body1: 'p',\n   *   body2: 'p',\n   *   inherit: 'p',\n   * }\n   */\n  variantMapping: PropTypes /* @typescript-to-proptypes-ignore */.object\n} : void 0;\nexport default Typography;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTypographyUtilityClass(slot) {\n  return generateUtilityClass('MuiTypography', slot);\n}\nconst typographyClasses = generateUtilityClasses('MuiTypography', ['root', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'inherit', 'button', 'caption', 'overline', 'alignLeft', 'alignRight', 'alignCenter', 'alignJustify', 'noWrap', 'gutterBottom', 'paragraph']);\nexport default typographyClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACA,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,QAAQ,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,aAAa,aAAa,SAAS,SAAS,WAAW,UAAU,WAAW,YAAY,aAAa,cAAc,eAAe,gBAAgB,UAAU,gBAAgB,WAAW,CAAC;AACvS,IAAO,4BAAQ;;;ADMf,yBAA4B;AAC5B,IAAM,WAAW;AAAA,EACf,SAAS;AAAA,EACT,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AAAA,EACT,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAChB;AACA,IAAM,eAAe,4BAA4B;AACjD,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,WAAW,UAAU,aAAa,QAAQ,mBAAW,KAAK,CAAC,IAAI,gBAAgB,gBAAgB,UAAU,UAAU,aAAa,WAAW;AAAA,EACrK;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AACO,IAAM,iBAAiB,eAAO,QAAQ;AAAA,EAC3C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,WAAW,OAAO,WAAW,OAAO,GAAG,WAAW,UAAU,aAAa,OAAO,QAAQ,mBAAW,WAAW,KAAK,CAAC,EAAE,GAAG,WAAW,UAAU,OAAO,QAAQ,WAAW,gBAAgB,OAAO,cAAc,WAAW,aAAa,OAAO,SAAS;AAAA,EACvR;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,MAAG;AAlDH;AAkDO;AAAA,IACL,QAAQ;AAAA,IACR,UAAU,CAAC;AAAA,MACT,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA;AAAA,QAEL,MAAM;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,MACjB;AAAA,IACF,GAAG,GAAG,OAAO,QAAQ,MAAM,UAAU,EAAE,OAAO,CAAC,CAAC,SAAS,KAAK,MAAM,YAAY,aAAa,SAAS,OAAO,UAAU,QAAQ,EAAE,IAAI,CAAC,CAAC,SAAS,KAAK,OAAO;AAAA,MAC1J,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT,EAAE,GAAG,GAAG,OAAO,QAAQ,MAAM,OAAO,EAAE,OAAO,+BAA+B,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,OAAO;AAAA,MAC/F,OAAO;AAAA,QACL;AAAA,MACF;AAAA,MACA,OAAO;AAAA,QACL,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,MAC9C;AAAA,IACF,EAAE,GAAG,GAAG,OAAO,UAAQ,WAAM,YAAN,mBAAe,SAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,OAAO,UAAU,QAAQ,EAAE,IAAI,CAAC,CAAC,KAAK,OAAO;AAAA,MACnH,OAAO;AAAA,QACL,OAAO,OAAO,mBAAW,KAAK,CAAC;AAAA,MACjC;AAAA,MACA,OAAO;AAAA,QACL,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK,KAAK;AAAA,MACjD;AAAA,IACF,EAAE,GAAG;AAAA,MACH,OAAO,CAAC;AAAA,QACN;AAAA,MACF,MAAM,WAAW,UAAU;AAAA,MAC3B,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,MACF,MAAM,WAAW;AAAA,MACjB,OAAO;AAAA,QACL,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,MACd;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,MACF,MAAM,WAAW;AAAA,MACjB,OAAO;AAAA,QACL,cAAc;AAAA,MAChB;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,MACF,MAAM,WAAW;AAAA,MACjB,OAAO;AAAA,QACL,cAAc;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,CAAE,CAAC;AACH,IAAM,wBAAwB;AAAA,EAC5B,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AACX;AACA,IAAM,aAAgC,iBAAW,SAASA,YAAW,SAAS,KAAK;AACjF,QAAM;AAAA,IACJ;AAAA,IACA,GAAG;AAAA,EACL,IAAI,gBAAgB;AAAA,IAClB,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM,YAAY,CAAC,SAAS,KAAK;AAEjC,QAAM,QAAQ,aAAa;AAAA,IACzB,GAAG;AAAA,IACH,GAAI,aAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,iBAAiB;AAAA,IACjB,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY,cAAc,YAAY,MAAM,eAAe,OAAO,KAAK,sBAAsB,OAAO,MAAM;AAChH,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,gBAAgB;AAAA,IACvC,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,GAAG;AAAA,IACH;AAAA,IACA,OAAO;AAAA,MACL,GAAI,UAAU,aAAa;AAAA,QACzB,0BAA0B;AAAA,MAC5B;AAAA,MACA,GAAG,MAAM;AAAA,IACX;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAwC,WAAW,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASpF,OAAO,kBAAAC,QAAU,MAAM,CAAC,UAAU,WAAW,WAAW,QAAQ,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,EAIxE,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,aAAa,WAAW,SAAS,QAAQ,WAAW,eAAe,iBAAiB,cAAc,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrN,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,SAAS,SAAS,UAAU,WAAW,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,WAAW,YAAY,aAAa,WAAW,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBpO,gBAAgB,kBAAAA,QAAgD;AAClE,IAAI;AACJ,IAAO,qBAAQ;", "names": ["Typography", "_jsx", "PropTypes"]}