{"version": 3, "sources": ["../../@mui/material/esm/utils/createSimplePaletteValueFilter.js"], "sourcesContent": ["/**\n * Type guard to check if the object has a \"main\" property of type string.\n *\n * @param obj - the object to check\n * @returns boolean\n */\nfunction hasCorrectMainProperty(obj) {\n  return typeof obj.main === 'string';\n}\n/**\n * Checks if the object conforms to the SimplePaletteColorOptions type.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param obj - The object to check\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns boolean\n */\nfunction checkSimplePaletteColorValues(obj, additionalPropertiesToCheck = []) {\n  if (!hasCorrectMainProperty(obj)) {\n    return false;\n  }\n  for (const value of additionalPropertiesToCheck) {\n    if (!obj.hasOwnProperty(value) || typeof obj[value] !== 'string') {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Creates a filter function used to filter simple palette color options.\n * The minimum requirement is that the object has a \"main\" property of type string, this is always checked.\n * Optionally, you can pass additional properties to check.\n *\n * @param additionalPropertiesToCheck - Array containing \"light\", \"dark\", and/or \"contrastText\"\n * @returns ([, value]: [any, PaletteColorOptions]) => boolean\n */\nexport default function createSimplePaletteValueFilter(additionalPropertiesToCheck = []) {\n  return ([, value]) => value && checkSimplePaletteColorValues(value, additionalPropertiesToCheck);\n}"], "mappings": ";AAMA,SAAS,uBAAuB,KAAK;AACnC,SAAO,OAAO,IAAI,SAAS;AAC7B;AAUA,SAAS,8BAA8B,KAAK,8BAA8B,CAAC,GAAG;AAC5E,MAAI,CAAC,uBAAuB,GAAG,GAAG;AAChC,WAAO;AAAA,EACT;AACA,aAAW,SAAS,6BAA6B;AAC/C,QAAI,CAAC,IAAI,eAAe,KAAK,KAAK,OAAO,IAAI,KAAK,MAAM,UAAU;AAChE,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAUe,SAAR,+BAAgD,8BAA8B,CAAC,GAAG;AACvF,SAAO,CAAC,CAAC,EAAE,KAAK,MAAM,SAAS,8BAA8B,OAAO,2BAA2B;AACjG;", "names": []}